// 动态设置根元素字体大小
function setRemUnit() {
    // 设计稿宽度为375px时，根元素字体大小为10px
    console.log(document.documentElement.clientWidth,'document.documentElement.clientWidth')
    if(document.documentElement.clientWidth<1000){
        const designWidth = 375;
        const rootValue = 37.5;
        const scale = document.documentElement.clientWidth / designWidth;
        document.documentElement.style.fontSize = `${rootValue * Math.min(scale, 2)}px`;
    }

  }
  
  // 初始化
  setRemUnit();
  
  // 监听窗口大小变化时重新计算
  window.addEventListener('resize', setRemUnit);
  window.addEventListener('orientationchange', setRemUnit);