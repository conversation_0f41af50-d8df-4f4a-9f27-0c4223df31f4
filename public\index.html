<!doctype html>
<html>

<head>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-cache, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no">

    <title>江苏省教育厅整治投诉平台</title>
    <style>
        *{padding: 0;margin: 0;}
        html,body{
            width: 100%;height: 100%;
        }
        .flex_center{
            display: flex;align-items: center;justify-content: center;
        }
       .index{
          width: 100vw;height: 100vh;position: relative;
          background: url(./assets/imgs/bg.png) no-repeat;
          background-size: 100% 100%;
          /* padding: 74px 90px; */
          overflow: hidden;
       }
       .logo{width: 447px;height: auto;margin: 74px 0 0 90px;display: block;}
       .middle{height: 222px;width: auto;margin: auto;display: block;margin-top: 130px;}
       .btns button{
          background: url(./assets/imgs/button.png) no-repeat;border: none;width: 248px;padding:18px 0 16px 0;
          background-size: 100% 100%;margin-top: 70px;
          font-size: 30px;color: #fff;line-height: 70px;cursor: pointer;
       }
       .btns button p{
        transform: translateX(-20px);
       }
       .btns button:first-child{margin-right: 160px;}
       .bottom{
          position: absolute;bottom:58px;left: 50%;transform: translateX(-50%);height: 90px;width: auto;
       }
       @media screen and (max-width:900px) {
         .index{display: flex;flex-direction: column;align-items: center;justify-content:flex-start;}
         .logo{width: 200px;height: auto;margin: 0;margin-bottom: 24px;margin-top: 96px;}
         .middle{width: 90%;max-width: 400px;height: auto;margin: 0;}
         .btns{
            flex-direction: column;width: 100%;
         }
         .btns button{max-width: 178px;width:50%;font-size: 18px;line-height: 40px;font-weight: bold;margin-top:32px;}
         .btns button:first-child{margin-right:0;}
         .bottom{width: 90%;height: auto;bottom: 28px;max-width: 400px;}
       }
       @media screen and (max-width:1100px){
        .middle{width: 90%;max-width: 600px;height: auto;}
       }
    </style>
</head>

<body>
    <div class="index">
          <img src="./assets/imgs/logo.png" alt="" class="logo">
          <img src="./assets/imgs/middle.png" alt="" class="middle">
          <div class="flex_center btns">
                <button onclick="tosu()">
                <p>我要投诉</p></button>
                <button onclick="search()">
                  <p>结果查询</p>
                </button>
          </div>
          <img src="./assets/imgs/bottom.png" alt="" class="bottom">
    </div>
   
    <script>
        function tosu(){
            location.href='https://form.tq-edu.com/s/iMRS1PTw'
        }
         function search(){
            location.href='http://localhost:5173/#/error'
         }

    </script>
</body>

</html>