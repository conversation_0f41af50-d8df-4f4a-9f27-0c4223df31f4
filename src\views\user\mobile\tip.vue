<template>
    <div class="mobile-container">
         <div class="flex_start tab">
            <div class="item flex_start flex_column">投诉信息</div>
            <div class="item flex_start flex_column active">反馈回复</div>
         </div>
         <div class="card">
            <div class="item flex_start">
                <p class="label">举报人真实姓名：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">性别：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">举报人身份：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">举报人所属地市：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">举报人所属县区：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">举报人学校名称：</p>
                <p class="value">张三</p>
            </div>
            <div class="item flex_start">
                <p class="label">举报分类：</p>
                <p class="value">张三</p>
            </div>
             <div class="item flex_start_0">
                <p class="label">举报内容：</p>
                <p class="value">张举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容举报内容三</p>
            </div>
            <div class="item">
                <p class="label">附件：</p>
                <div class="value">
                    <div  class="value flex_start flex_wrap">
                       <img src="@/assets/images/user/tips-bg.png" alt="" v-for="item in 6" :key="item">
                    </div>
                    <div>
                        <div class="file flex_between">
                            <p class="file-name">处理情理情理情理情理情况说明.doc</p>
                            <el-icon><Download /></el-icon>
                        </div>
                    </div>
                </div>
            </div>
         </div>
         <button class="back">返回</button>
    </div>
</template>
<style scoped lang='scss'>
@use '@/assets/styles/mobile-public.scss';
.mobile-container{
    background:#f9fafb url(@/assets/images/user/tips-bg.png) no-repeat;
    background-size: 100% 40vh;
    min-height: 100vh;
    overflow: auto;
   .tab{
        font-size: 16px;
        color: rgba(255,255,255,0.8);
        line-height: 20px;
           width: 90%;
           margin: 12px auto 0 auto;
        max-width: 350px;
        .item{
            margin-right: 30px;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 20px;
            padding: 10px 10px 0 10px;
           &::after{
             content: '';
            display: block;
            width:120%;
            height:2px;
            background: transparent;
            margin-top: 8px;
           }
        }
        .active{
            font-weight: bold;
            &::after{
             background: #fff;
           }
        }
   }
   .card{
        background: #F9FAFB;
        border-radius: 12px 12px 12px 12px;
        border: 1px solid #E0E0E0;
        padding: 16px;
        width: 90%;
        margin: 12px auto 0 auto;
        max-width: 350px;
    .item{
        margin-bottom: 16px;
       
        .label{
           color: #979797;
           font-size: 14px;
           line-height: 20px;
           font-weight: bold;
           white-space: nowrap;
        }
        .value{
           color: #374151;
           font-size: 14px;
           line-height: 20px;
           img{
            width:40%;
            height: 80px;
            object-fit: cover;
            margin-right: 8px;
            margin-top: 8px;
           }
        }
        .file{
            padding: 6px 14px;
            background: #FFFFFF;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #E2E3E6;
            width: 204px;
            font-size: 14px;
            color: #2B2C33;
            line-height: 24px;
            cursor: pointer;
            margin-top: 8px;
            .file-name{
                width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space:nowrap
            }
        }
    }
   }
   
}
</style>