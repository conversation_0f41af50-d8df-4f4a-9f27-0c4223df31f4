// import type { RouteRecordRaw } from "vue-router"
import { pinia } from "@/store"
import { constantRoutes, dynamicRoutes } from "@/router"
import { routerConfig, roleFirstRouteMap } from "@/router/config"
import { flatMultiLevelRoutes } from "@/router/helper"

function hasPermission(roles, route) {
  const routeRoles = route.meta?.roles
  return routeRoles ? roles.some(role => routeRoles.includes(role)) : true
}

function filterDynamicRoutes(routes, roles) {
  const res = []
  routes.forEach((route) => {
    const tempRoute = { ...route }
    if (hasPermission(roles, tempRoute)) {
      if (tempRoute.children) {
        tempRoute.children = filterDynamicRoutes(tempRoute.children, roles)
      }
      res.push(tempRoute)
    }
  })
  return res
}

export const usePermissionStore = defineStore("permission", () => {
  const routes = ref([])
  const addRoutes = ref([])
  const firstRoute = ref(null)

  // 根据用户角色获取首个路由
  const getFirstRouteByRoles = (roles) => {
    // 优先级：3 > 2 > 1
    const rolePriority = ["3", "2", "1"]

    for (const role of rolePriority) {
      if (roles.includes(role) && roleFirstRouteMap[role]) {
        return roleFirstRouteMap[role]
      }
    }

    // 如果没有匹配的角色，返回第一个有权限的路由
    return getFirstAccessibleRoute(filterDynamicRoutes(dynamicRoutes, roles))
  }

  // 获取第一个可访问的路由（备用方案）
  const getFirstAccessibleRoute = (accessedRoutes) => {
    for (const route of accessedRoutes) {
      if (route.children?.length > 0) {
        const firstChild = route.children[0]
        return `${route.path}/${firstChild.path}`.replace('//', '/')
      }
    }
    return null
  }

  const setRoutes = (roles) => {
    const accessedRoutes = filterDynamicRoutes(dynamicRoutes, roles)
    firstRoute.value = getFirstRouteByRoles(roles)
    set(accessedRoutes)
  }

  const setAllRoutes = () => {
    // 当不使用动态路由时，默认使用第一个路由
    firstRoute.value = getFirstAccessibleRoute(dynamicRoutes)
    set(dynamicRoutes)
  }

  const set = (accessedRoutes) => {
    routes.value = constantRoutes.concat(accessedRoutes)
    addRoutes.value = routerConfig.thirdLevelRouteCache ? flatMultiLevelRoutes(accessedRoutes) : accessedRoutes
  }

  return { routes, addRoutes, firstRoute, setRoutes, setAllRoutes, getFirstRouteByRoles }
})

/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function usePermissionStoreOutside() {
  return usePermissionStore(pinia)
}

