/** 项目标题 */
const VITE_APP_TITLE = import.meta.env.VITE_APP_TITLE || "江苏省教育厅政治投诉平台"

/** 动态标题 */
const dynamicTitle = ref("")

/** 设置标题 */
function setTitle(title) {
  dynamicTitle.value = title ? `${VITE_APP_TITLE} | ${title}` : VITE_APP_TITLE
}

// 监听标题变化
watch(dynamicTitle, (value, oldValue) => {
  if (document && value !== oldValue) {
    document.title = value
  }
})

/** 标题 Composable */
export function useTitle() {
  return { setTitle }
}
