<template>
  <div class="mobile-container">
    <el-dialog
      v-model="dialogVisible"
      title="Tips"
      width="80%"
      :before-close="handleClose"
    >
      <h3>通知</h3>
      <p class="content">预计8月15日上线，敬请期待！</p>
      <p class="confirm">确认</p>
    </el-dialog>
  </div>
</template>
<script setup>
import {ref} from 'vue'
 let dialogVisible=ref(true)
</script>

<style lang='scss' scoped>

@use '@/assets/styles/mobile-public.scss';
:deep(.el-dialog__header){
  display: none;
}
:deep(.el-dialog){
  padding: 20px 0 0 0;
  max-width: 350px;
}
h3{
  font-size: 15px;
  color: #333333;
  line-height: 21px;
  text-align: center;
}
.content{
  font-size: 15px;
  color: #333333;
  line-height: 21px;text-align: center;
 padding: 10px 0 20px 0;
  border-bottom: 1px solid #EEEEEE;
}
.confirm{
  font-size: 16px;
color: #09B1EA;
line-height: 25px;
cursor: pointer;
padding: 10px 0;
text-align: center;
}
</style>