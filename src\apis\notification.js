import { request } from "@/utils/axios"

/** 获取通知列表 */
export function getNotificationListApi(params) {
  return request({
    url: "notifications",
    method: "get",
    params
  })
}

/** 标记通知为已读 */
export function markNotificationReadApi(id) {
  return request({
    url: `notifications/${id}/read`,
    method: "post"
  })
}

/** 标记所有通知为已读 */
export function markAllNotificationsReadApi() {
  return request({
    url: "notifications/read-all",
    method: "post"
  })
}

/** 删除通知 */
export function deleteNotificationApi(id) {
  return request({
    url: `notifications/${id}`,
    method: "delete"
  })
}

/** 获取未读通知数量 */
export function getUnreadNotificationCountApi() {
  return request({
    url: "notifications/unread-count",
    method: "get"
  })
}

/** 修改密码 */
export function changePasswordApi(data) {
  return request({
    url: "auth/change-password",
    method: "post",
    data
  })
}
