<script setup>
import { useLayoutMode } from "@/hooks/useLayoutMode"

// interface Props {
//   collapse?: boolean
// }

const props = defineProps({
  collapse: {
    type: Boolean,
    default: true
  }
})

const { isLeft, isTop } = useLayoutMode()
</script>

<template>
  <div class="layout-logo-container" :class="{ 'collapse': props.collapse, 'layout-mode-top': isTop }">
    <router-link to="/" class="logo-link">
      <span class="title">江苏省教育厅整治投诉平台</span>
    </router-link>
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  position: relative;
  width: 100%;
  height: var(--v3-header-height);
  line-height: var(--v3-header-height);
  text-align: left;
  overflow: hidden;
  .layout-logo {
    display: none;
  }
  .layout-logo-text {
    height: 100%;
    vertical-align: middle;
  }
}

.layout-mode-top {
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
}

.logo-link {
  display: inline-block;
  margin-left: 32px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  background: linear-gradient(90deg, #1A68A8 0%, #389AD3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  line-height: 28px;
}
</style>
