<script setup>
import { ArrowRight } from '@element-plus/icons-vue';

const route = useRoute()
const router = useRouter()
</script>

<template>
  <div class="simply-breadcrumb flex-start-center mb20">
    <div class="parent text-[#6D6F75] pointer font-[14px]" @click="router.push('/parentRoute')">{{ route.meta.parentRouteTitle }}</div>
    <el-icon class="mx-[6px]"><ArrowRight /></el-icon>
    <div class="current text-[#2B2C33] font-[14px]">{{ route.meta.title }}</div>
 </div>
</template>

<style lang="scss" scoped>

</style>
