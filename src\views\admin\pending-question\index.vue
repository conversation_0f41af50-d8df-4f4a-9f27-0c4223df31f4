<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, UploadFilled, Close, Document } from "@element-plus/icons-vue";

const router = useRouter()

const searchForm = ref({
  startDate: "", // 开始时间
  endDate: "", // 结束时间
  questionType: "", // 问题分类
  schoolType: "", // 学校类型
  status: "", // 状态
  content: "", // 内容
});

// 问题分类选项
const questionTypeOptions = [
  { label: "全部", value: "" },
  { label: "食品安全", value: "food_safety" },
  { label: "膳食经费", value: "meal_funding" },
  { label: "教辅教材征订", value: "textbook_ordering" },
  { label: "校服定制采购", value: "uniform_procurement" },
  { label: "校外培训", value: "external_training" },
  { label: "其他", value: "other" },
];

// 学校类型选项
const schoolTypeOptions = [
  { label: "全部", value: "" },
  { label: "幼儿园", value: "kindergarten" },
  { label: "小学", value: "primary" },
  { label: "初中", value: "middle" },
  { label: "高中", value: "high" },
  { label: "大学", value: "university" },
];

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "待处理", value: "pending_processing" },
  { label: "待审核", value: "pending_review" },
  { label: "待处理（省厅督办件）", value: "pending_provincial" },
];

// 日期验证：结束时间不能早于开始时间
const validateDateRange = () => {
  if (searchForm.value.startDate && searchForm.value.endDate) {
    if (
      new Date(searchForm.value.endDate) < new Date(searchForm.value.startDate)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      searchForm.value.endDate = "";
    }
  }
};

// 开始时间变化时的处理
const handleStartDateChange = () => {
  validateDateRange();
};

// 结束时间变化时的处理
const handleEndDateChange = () => {
  validateDateRange();
};

// 搜索功能
const handleSearch = () => {
  // 验证日期范围
  if (searchForm.value.startDate && searchForm.value.endDate) {
    if (
      new Date(searchForm.value.endDate) < new Date(searchForm.value.startDate)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      return;
    }
  }

  console.log("搜索参数:", searchForm.value);
  // TODO: 实现搜索逻辑
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    startDate: "",
    endDate: "",
    questionType: "",
    schoolType: "",
    status: "",
    content: "",
  };
  console.log("表单已重置");
};

// 表格数据
const tableData = ref([
  {
    id: "10001",
    district: "玄武区",
    reporterInfo: "第三年级\n156****5581\n学生家长",
    problemType: "膳食经费",
    schoolType: "大学",
    reportedSchool: "南京理工大学",
    problemDescription: "学校收费标准不明确，希望提供详细的收费清单。",
    replyContent: "已核实相关情况，学校将在官网公布详细收费清单。",
    replyTime: "2025-07-25",
    handlingUnit: "南京市教育局",
    status: "待处理",
  },
  {
    id: "10002",
    district: "姑苏区",
    reporterInfo: "第三年级\n156****5581\n学生家长",
    problemType: "校外培训",
    schoolType: "初中",
    reportedSchool: "苏州市第三中学",
    problemDescription:
      "教师课堂管理不当，希望学校加强师德师风建设和教师培训工作。",
    replyContent: "",
    replyTime: "",
    handlingUnit: "苏州市教育局",
    status: "待处理（省厅督办件）",
  },
  {
    id: "10003",
    district: "崇川区",
    reporterInfo: "第三年级\n156****5581\n学生家长",
    problemType: "食品安全",
    schoolType: "初中",
    reportedSchool: "南通市第一中学",
    problemDescription: "学校周边存在安全隐患问题，希望相关部门加强管理。",
    replyContent: "已联系相关部门进行整改，将在一周内完成安全隐患排查。",
    replyTime: "2025-07-20",
    handlingUnit: "南通市教育局",
    status: "待审核",
  },
  {
    id: "10004",
    district: "新北区",
    reporterInfo: "第三年级\n156****5581\n学生家长",
    problemType: "教辅教材征订",
    schoolType: "初中",
    reportedSchool: "常州市第三中学",
    problemDescription: "学校教育教学质量有待提高，希望加强教师培训。",
    replyContent: "",
    replyTime: "",
    handlingUnit: "常州市教育局",
    status: "待处理",
  },
  {
    id: "10005",
    district: "京口区",
    reporterInfo: "第三年级\n156****5581\n学生家长",
    problemType: "校服定制采购",
    schoolType: "大学",
    reportedSchool: "江苏理工学院",
    problemDescription: "学校收费标准不明确，希望提供详细的收费清单。",
    replyContent: "正在核实相关情况，预计本周内给出回复。",
    replyTime: "2025-07-18",
    handlingUnit: "镇江市教育局",
    status: "待审核",
  },
]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 操作处理
const handleOperation = (row, action) => {
  if (action === 'reply') {
    // 问题回复
    router.push({
      path: '/pendingQuestion/reply',
      query: {
        id: row.id,
        type: 'reply'
      }
    })
  } else if (action === 'review') {
    // 问题审核
    router.push({
      path: '/pendingQuestion/review',
      query: {
        id: row.id,
        type: 'review'
      }
    })
  }
};

// 获取状态CSS类名
const getStatusClass = (status) => {
  const classMap = {
    待处理: "status-pending-processing",
    待审核: "status-pending-review",
    "待处理（省厅督办件）": "status-pending-provincial",
  };
  return classMap[status] || "status-default";
};

// 根据状态获取操作按钮文本和类型
const getOperationInfo = (status) => {
  if (status === "待处理" || status === "待处理（省厅督办件）") {
    return {
      text: "问题回复",
      action: "reply",
      class: "blue2"
    };
  } else if (status === "待审核") {
    return {
      text: "问题审核",
      action: "review",
      class: "blue2"
    };
  }
  return null;
};

// 获取问题分类CSS类名
const getProblemTypeClass = (problemType) => {
  const classMap = {
    食品安全: "problem-type-food-safety",
    膳食经费: "problem-type-meal-funding",
    校外培训: "problem-type-external-training",
    教辅教材征订: "problem-type-textbook-ordering",
    校服定制采购: "problem-type-uniform-procurement",
    其他: "problem-type-other",
  };
  return classMap[problemType] || "problem-type-other";
};

// 申请延期弹窗相关
const extensionDialogVisible = ref(false);
const extensionFormRef = ref();
const extensionForm = ref({
  days: 1,
  attachments: []
});

// 申请延期表单验证规则
const extensionFormRules = {
  days: [
    { required: true, message: '请输入延期时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 30, message: '延期时间必须在1-30天之间', trigger: 'blur' }
  ]
};

// 当前操作的行数据
const currentRow = ref(null);

// 文件上传相关
const fileInputRef = ref();
const fileList = ref([]);
const isDragOver = ref(false);

// 支持的文件格式
const allowedFormats = ['jpg', 'jpeg', 'png', 'doc', 'docx', 'pdf'];

// 文件验证
const validateFile = (file) => {
  const fileExtension = file.name.split('.').pop().toLowerCase();

  if (!allowedFormats.includes(fileExtension)) {
    ElMessage.error('只支持 jpg/doc/docx/pdf/png 格式的文件');
    return false;
  }

  if (fileList.value.length >= 5) {
    ElMessage.error('最多只能上传5个文件');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB');
    return false;
  }

  return true;
};

// 处理文件选择
const handleFileSelect = (files) => {
  const validFiles = Array.from(files).filter(validateFile);

  for (const file of validFiles) {
    const fileItem = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: getFileType(file.name),
      file: file,
      url: URL.createObjectURL(file)
    };

    fileList.value.push(fileItem);
  }
};

// 点击上传区域
const handleUploadClick = () => {
  fileInputRef.value?.click();
};

// 文件输入变化
const handleFileInputChange = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = '';
};

// 拖拽相关事件
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
};

// 删除文件
const removeFile = (fileId) => {
  const index = fileList.value.findIndex(file => file.id === fileId);
  if (index > -1) {
    // 释放URL对象
    if (fileList.value[index].url.startsWith('blob:')) {
      URL.revokeObjectURL(fileList.value[index].url);
    }
    fileList.value.splice(index, 1);
  }
};

// 获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  if (['jpg', 'jpeg', 'png'].includes(extension)) return 'img';
  if (['doc', 'docx'].includes(extension)) return 'doc';
  if (extension === 'pdf') return 'pdf';
  return 'doc';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 打开申请延期弹窗
const handleExtensionRequest = (row) => {
  currentRow.value = row;
  extensionDialogVisible.value = true;
  // 重置表单
  extensionForm.value = {
    days: 1,
    attachments: []
  };
  // 清空文件列表
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 关闭申请延期弹窗
const handleCloseExtensionDialog = () => {
  extensionDialogVisible.value = false;
  currentRow.value = null;
  // 清理文件列表和URL对象
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 提交申请延期
const submitExtensionRequest = async () => {
  try {
    await extensionFormRef.value.validate();

    const params = {
      questionId: currentRow.value.id,
      extensionDays: extensionForm.value.days,
      attachments: fileList.value.map(file => ({
        name: file.name,
        url: file.url // 实际项目中这里应该是上传后的URL
      }))
    };

    console.log('申请延期参数:', params);

    // TODO: 调用申请延期API
    // await extensionRequestApi(params);

    ElMessage.success('申请延期提交成功');
    handleCloseExtensionDialog();

    // 刷新列表
    // handleSearch();
  } catch (error) {
    console.error('申请延期失败:', error);
  }
};

// 申请延期弹窗相关
const extensionDialogVisible = ref(false);
const extensionFormRef = ref();
const extensionForm = ref({
  days: 1,
  attachments: []
});

// 申请延期表单验证规则
const extensionFormRules = {
  days: [
    { required: true, message: '请输入延期时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 30, message: '延期时间必须在1-30天之间', trigger: 'blur' }
  ]
};

// 当前操作的行数据
const currentRow = ref(null);

// 文件上传相关
const fileInputRef = ref();
const fileList = ref([]);
const isDragOver = ref(false);

// 支持的文件格式
const allowedFormats = ['jpg', 'jpeg', 'png', 'doc', 'docx', 'pdf'];

// 文件验证
const validateFile = (file) => {
  const fileExtension = file.name.split('.').pop().toLowerCase();

  if (!allowedFormats.includes(fileExtension)) {
    ElMessage.error('只支持 jpg/doc/docx/pdf/png 格式的文件');
    return false;
  }

  if (fileList.value.length >= 5) {
    ElMessage.error('最多只能上传5个文件');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB');
    return false;
  }

  return true;
};

// 处理文件选择
const handleFileSelect = (files) => {
  const validFiles = Array.from(files).filter(validateFile);

  for (const file of validFiles) {
    const fileItem = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: getFileType(file.name),
      file: file,
      url: URL.createObjectURL(file)
    };

    fileList.value.push(fileItem);
  }
};

// 点击上传区域
const handleUploadClick = () => {
  fileInputRef.value?.click();
};

// 文件输入变化
const handleFileInputChange = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = '';
};

// 拖拽相关事件
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
};

// 删除文件
const removeFile = (fileId) => {
  const index = fileList.value.findIndex(file => file.id === fileId);
  if (index > -1) {
    // 释放URL对象
    if (fileList.value[index].url.startsWith('blob:')) {
      URL.revokeObjectURL(fileList.value[index].url);
    }
    fileList.value.splice(index, 1);
  }
};

// 获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  if (['jpg', 'jpeg', 'png'].includes(extension)) return 'img';
  if (['doc', 'docx'].includes(extension)) return 'doc';
  if (extension === 'pdf') return 'pdf';
  return 'doc';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 打开申请延期弹窗
const handleExtensionRequest = (row) => {
  currentRow.value = row;
  extensionDialogVisible.value = true;
  // 重置表单
  extensionForm.value = {
    days: 1,
    attachments: []
  };
  // 清空文件列表
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 关闭申请延期弹窗
const handleCloseExtensionDialog = () => {
  extensionDialogVisible.value = false;
  currentRow.value = null;
  // 清理文件列表和URL对象
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 提交申请延期
const submitExtensionRequest = async () => {
  try {
    await extensionFormRef.value.validate();

    const params = {
      questionId: currentRow.value.id,
      extensionDays: extensionForm.value.days,
      attachments: fileList.value.map(file => ({
        name: file.name,
        url: file.url // 实际项目中这里应该是上传后的URL
      }))
    };

    console.log('申请延期参数:', params);

    // TODO: 调用申请延期API
    // await extensionRequestApi(params);

    ElMessage.success('申请延期提交成功');
    handleCloseExtensionDialog();

    // 刷新列表
    // handleSearch();
  } catch (error) {
    console.error('申请延期失败:', error);
  }
};


</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="待处理问题"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-2" label-position="top">
        <!-- 第一行 -->
        <el-form-item label="留言时间段">
          <div class="w-[100%] flex justify-between items-center gap-[8px]">
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              @change="handleStartDateChange"
            />
            <span style="color: #606266; line-height: 48px">至</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              :disabled-date="
                (time) =>
                  searchForm.startDate && time < new Date(searchForm.startDate)
              "
              @change="handleEndDateChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="问题分类">
          <el-select v-model="searchForm.questionType" placeholder="全部">
            <el-option
              v-for="item in questionTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="学校类型">
          <el-select v-model="searchForm.schoolType" placeholder="全部">
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            placeholder="请输入关键词"
            style="width: 240px"
          />
        </el-form-item>

        <!-- 填空的 -->
        <el-form-item></el-form-item>
        <el-form-item></el-form-item>

        <!-- 按钮组 -->
        <el-form-item class="hidden-label" label="按钮">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 导出按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <el-button class="common-button-3" @click="handleExport">
          <template #icon>
            <img
              src="@/assets/images/common/export.png"
              alt=""
              width="16"
              height="16"
            />
          </template>
          导出数据
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 固定左侧列 -->
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          fixed="left"
          align="center"
        />
        <el-table-column
          prop="district"
          label="地区"
          width="100"
          fixed="left"
          align="center"
        />
        <el-table-column
          label="举报人信息"
          width="120"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            <div class="whitespace-pre-line">{{ row.reporterInfo }}</div>
          </template>
        </el-table-column>

        <!-- 其他列 -->
        <el-table-column label="问题分类" width="160" align="center">
          <template #default="{ row }">
            <span
              :class="[
                'problem-type-tag',
                getProblemTypeClass(row.problemType),
              ]"
            >
              {{ row.problemType }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="schoolType"
          label="学校类型"
          width="100"
          align="center"
        />
        <el-table-column
          prop="reportedSchool"
          label="举报学校名称"
          width="150"
          align="center"
        />
        <el-table-column label="举报内容" min-width="200" align="center">
          <template #default="{ row }">
            <div>{{ row.problemDescription }}</div>
          </template>
        </el-table-column>
        <el-table-column label="回复内容" min-width="200" align="center">
          <template #default="{ row }">
            <div>{{ row.replyContent || '暂无回复' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="replyTime"
          label="回复时间"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.replyTime || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="handlingUnit"
          label="办理单位"
          width="120"
          align="center"
        />
        <el-table-column label="状态" width="180" align="center">
          <template #default="{ row }">
            <span :class="['status-tag', getStatusClass(row.status)]">
              {{ row.status }}
            </span>
          </template>
        </el-table-column>

        <!-- 固定右侧操作列 -->
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <div class="flex-center-center">
              <div
                v-if="getOperationInfo(row.status)"
                :class="['pointer', getOperationInfo(row.status).class]"
                @click="handleOperation(row, getOperationInfo(row.status).action)"
              >
                {{ getOperationInfo(row.status).text }}
              </div>
              <div class="blue3 pointer" @click="handleExtensionRequest(row)">
                申请延期
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 申请延期弹窗 -->
    <el-dialog
      v-model="extensionDialogVisible"
      title="申请延期"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseExtensionDialog"
    >
      <el-form
        ref="extensionFormRef"
        :model="extensionForm"
        :rules="extensionFormRules"
        label-position="top"
        class="extension-form"
      >
        <el-form-item label="延期时间" prop="days" required>
          <div class="flex items-center gap-2">
            <el-input-number
              v-model="extensionForm.days"
              :min="1"
              :max="30"
              :step="1"
              controls-position="right"
              style="width: 120px"
            />
            <span class="text-gray-600">天</span>
          </div>
        </el-form-item>

        <el-form-item required>
          <template #label>
            <span class="attachment-label">
              附件
              <span class="attachment-hint">（不超过5个，支持jpg/doc/pdf/png格式）</span>
            </span>
          </template>
          <div class="upload-area">
            <!-- 隐藏的文件输入 -->
            <input
              ref="fileInputRef"
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.doc,.docx,.pdf"
              style="display: none"
              @change="handleFileInputChange"
            />

            <!-- 自定义上传区域 -->
            <div
              class="custom-upload-dragger"
              :class="{ 'drag-over': isDragOver }"
              @click="handleUploadClick"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
              @drop="handleDrop"
            >
              <div class="upload-content">
                <el-icon color="#1A68A8" size="48">
                  <UploadFilled />
                </el-icon>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">支持jpg、doc、pdf、png格式，最多上传5个文件</div>
              </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list" v-if="fileList.length > 0">
              <div v-for="file in fileList" :key="file.id" class="file-item">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <el-icon class="remove-icon" @click="removeFile(file.id)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseExtensionDialog">取消</el-button>
          <el-button type="primary" @click="submitExtensionRequest">申请</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 申请延期弹窗样式
.extension-form {
  .attachment-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey1);

    .attachment-hint {
      font-size: 14px;
      font-weight: 400;
      color: #94959C;
      margin-left: 8px;
    }
  }

  .upload-area {
    width: 100%;

    .custom-upload-dragger {
      background: #ffffff;
      border: 1px dashed #E2E3E6;
      border-radius: 8px;
      width: 100%;
      height: 126px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover,
      &.drag-over {
        border-color: #0EC3ED;
        background: #F8FCFF;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-align: center;
        pointer-events: none;

        .el-icon {
          width: 33px;
          height: 24px;
        }

        .upload-text {
          font-size: 16px;
          color: #2B2C33;
          line-height: 24px;
          margin-top: 6px;
        }

        .upload-hint {
          font-size: 14px;
          color: #94959C;
          line-height: 20px;
        }
      }
    }

    .file-list {
      margin-top: 16px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #F8F9FA;
        border-radius: 6px;
        margin-bottom: 8px;

        .file-icon {
          font-size: 16px;
          color: #1A68A8;
          margin-right: 8px;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #2B2C33;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .remove-icon {
          font-size: 16px;
          color: #94959C;
          cursor: pointer;
          margin-left: 8px;

          &:hover {
            color: #F56C6C;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
