# 督办意见功能完善总结

## 完成的功能

### 1. 添加办理单位单选框
在督办意见表单中添加了办理单位选择功能：

#### 数据结构
```javascript
// 办理单位选项
const handleUnitOptions = [
  { label: '省厅', value: 'provincial' },
  { label: '地市', value: 'city' },
  { label: '县区', value: 'county' }
];

// 回复表单数据中添加办理单位字段
const replyForm = ref({
  content: '',
  handlerName: '',
  handlerPhone: '',
  attachments: [],
  handleUnit: '' // 办理单位
});
```

#### 表单组件
```vue
<!-- 办理单位 (仅督办意见显示) -->
<el-form-item v-if="type === 'add-supervise'" label="办理单位" prop="handleUnit" class="mb-[20px]">
  <el-radio-group v-model="replyForm.handleUnit" class="handle-unit-radio">
    <el-radio v-for="option in handleUnitOptions" :key="option.value" :value="option.value">
      {{ option.label }}
    </el-radio>
  </el-radio-group>
</el-form-item>
```

### 2. 完善submitSupervise函数
对接了addSuperviseApi接口，实现了完整的督办意见提交功能：

#### API接口对接
```javascript
// 提交督办意见
const submitSupervise = async () => {
  await replyFormRef.value.validate();

  // 等待所有文件上传完成
  const uploadingFiles = fileList.value.filter(file => file.uploading);
  if (uploadingFiles.length > 0) {
    ElMessage.warning('文件正在上传中，请稍候...');
    return;
  }

  // 组织文件数据为name和url组成的数组对象
  const superviseFiles = fileList.value.map(file => ({
    name: file.name,
    url: file.cosUrl
  }));

  const params = {
    complaintId: route.query.complaintId,
    superviseOpinion: replyForm.value.content,
    superviseFiles: superviseFiles
  };

  await addSuperviseApi(params);
  ElMessage.success('督办意见提交成功');

  // 重置表单
  replyForm.value = {
    content: '',
    handlerName: '',
    handlerPhone: '',
    attachments: [],
    handleUnit: ''
  };

  // 清理文件列表和URL对象
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];

  // 返回上一页
  router.back();
};
```

### 3. 表单验证增强
将表单验证规则改为计算属性，动态添加办理单位验证：

```javascript
// 表单验证规则
const replyRules = computed(() => {
  const rules = {
    content: [
      { required: true, message: type === 'add-supervise' ? '请输入督办意见' : '请输入回复内容', trigger: 'blur' }
    ],
    handlerName: [
      { required: true, message: '请输入经办人姓名', trigger: 'blur' }
    ],
    handlerPhone: [
      { required: true, message: '请输入经办人电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
  };

  // 如果是督办意见，添加办理单位验证
  if (type === 'add-supervise') {
    rules.handleUnit = [
      { required: true, message: '请选择办理单位', trigger: 'change' }
    ];
  }

  return rules;
});
```

### 4. 样式优化
为办理单位单选框添加了专门的样式：

```scss
.handle-unit-radio {
  display: flex;
  gap: 24px;
  
  :deep(.el-radio) {
    margin-right: 0;
    
    .el-radio__label {
      font-size: 16px;
      color: #2B2C33;
    }
    
    .el-radio__input.is-checked .el-radio__inner {
      background-color: #0EC3ED;
      border-color: #0EC3ED;
    }
  }
}
```

## API接口参数
submitSupervise函数调用addSuperviseApi接口，传递参数：
- `complaintId`: 投诉ID
- `superviseOpinion`: 督办意见内容
- `superviseFiles`: 文件数组，每个文件包含name和url字段

## 功能特点
1. **条件显示**: 办理单位单选框仅在type为'add-supervise'时显示
2. **表单验证**: 督办意见模式下办理单位为必选项
3. **文件上传**: 支持督办附件上传，与回复附件功能一致
4. **统一管理**: 通过统一的handleSubmit函数调用
5. **用户体验**: 提供清晰的成功提示和错误处理

## 测试建议
1. 测试办理单位单选框的显示和选择功能
2. 测试表单验证，确保办理单位为必选
3. 测试督办意见和文件的提交功能
4. 测试提交成功后的表单重置和页面跳转
