import { request } from "@/utils/axios";

/* 分页查询学校列表 */
export function getSchoolListApi(params) {
  return request({
    url: "system/school/list",
    method: "get",
    params,
  });
}

/* 新增学校 */
export function addSchoolApi(data) {
  return request({
    url: "system/school",
    method: "post",
    data,
  });
}

/* 删除学校 */
export function deleteSchoolApi(schoolId) {
  return request({
    url: `system/school/${schoolId}`,
    method: "delete",
  });
}

/* 根据id查看详情 */
export function getSchoolDetailApi(schoolId) {
  return request({
    url: `system/school/${schoolId}`,
    method: "get",
  });
}

/* 更新学校信息 */
export function updateSchoolApi(schoolId, data) {
  return request({
    url: `system/school/${schoolId}`,
    method: "put",
    data,
  });
}

/* 下载模版 */
export function downloadTemplateApi() {
  return request({
    url: "system/school/template",
    method: "get",
    responseType: "blob",
  });
}

/* 批量导入 */
export function batchImport<PERSON>pi(data) {
  return request({
    url: "system/school/import",
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
