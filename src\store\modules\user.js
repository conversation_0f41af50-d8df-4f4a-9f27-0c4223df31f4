import { getCurrent<PERSON>ser<PERSON><PERSON> } from "@/apis/users"
import { getNotificationListApi, markNotificationReadApi } from "@/apis/notification"
import { setToken as _setToken, getToken, removeToken } from "@/utils/cache/cookies"
import { pinia } from "@/store"
import { resetRouter } from "@/router"
import { routerConfig } from "@/router/config"
import { useSettingsStore } from "./settings"
import { useTagsViewStore } from "./tags-view"

export const useUserStore = defineStore("user", () => {
  const token = ref(getToken() || "")

  const roles = ref([])

  const username = ref("")

  const roleName = ref("")

  // 用户完整信息
  const userInfo = ref({})

  // 通知相关状态
  const notifications = ref([])
  const hasUnreadNotifications = ref(false)
  const notificationLoading = ref(false)

  const tagsViewStore = useTagsViewStore()

  const settingsStore = useSettingsStore()

  // 设置 Token
  const setToken = (value) => {
    _setToken(value)
    token.value = value
  }

  // 获取用户详情
  const getInfo = async () => {
    const { data } = await getCurrentUserApi()
    username.value = data.title
    // 存储完整的用户信息
    userInfo.value = data
    // 验证返回的 roles 是否为一个非空数组，否则塞入一个没有任何作用的默认角色，防止路由守卫逻辑进入无限循环

    // 3 省级 2 市级 1 区级 0 普通用户

    roles.value = data.role ? [data.role?.toString()] : routerConfig.defaultRoles;

    if (data.role === 1) {
      roleName.value = "区级管理员"
    } else if (data.role === 2) {
      roleName.value = "市级管理员"
    } else if (data.role === 3) {
      roleName.value = '省级管理员'
    }
  }

  // 模拟角色变化
  const changeRoles = (role) => {
    const newToken = `token-${role}`
    token.value = newToken
    _setToken(newToken)
    // 用刷新页面代替重新登录
    location.reload()
  }

  // 登出
  const logout = () => {
    removeToken()
    token.value = ""
    roles.value = []
    userInfo.value = {}
    clearNotifications()
    resetRouter()
    resetTagsView()
  }

  // 重置 Token
  const resetToken = () => {
    removeToken()
    token.value = ""
    roles.value = []
    userInfo.value = {}
  }

  // 重置 Visited Views 和 Cached Views
  const resetTagsView = () => {
    if (!settingsStore.cacheTagsView) {
      tagsViewStore.delAllVisitedViews()
      tagsViewStore.delAllCachedViews()
    }
  }

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      notificationLoading.value = true;
      // const { data } = await getNotificationListApi()
      // notifications.value = data.list || []

      // 模拟数据
      notifications.value = [
        {
          id: 1,
          title: "您有 1 条新的待审核问题",
          content: "您有 1 条新的待审核问题",
          createTime: "2024-12-16 15:22:49",
          isRead: false,
        },
        {
          id: 2,
          title: "您有 1 条新的待分办问题",
          content: "您有 1 条新的待分办问题",
          createTime: "2024-12-16 15:22:49",
          isRead: false,
        },
        {
          id: 3,
          title: "您有 1 条新的督办件问题",
          content: "您有 1 条新的督办件问题",
          createTime: "2024-12-16 15:22:49",
          isRead: false,
        },
        {
          id: 4,
          title: "您有 1 条新的审核未通过问题",
          content: "您有 1 条新的审核未通过问题",
          createTime: "2024-12-16 15:22:49",
          isRead: true,
        },
        {
          id: 5,
          title: "您有 1 条新的督办件问题",
          content: "您有 1 条新的督办件问题",
          createTime: "2024-12-16 15:22:49",
          isRead: true,
        },
      ];
      hasUnreadNotifications.value = notifications.value.some(
        (item) => !item.isRead
      );
    } catch (error) {
      console.error('获取通知列表失败:', error)
    } finally {
      notificationLoading.value = false
    }
  }

  // 标记通知为已读
  const markNotificationAsRead = async (notificationId) => {
    const notification = notifications.value.find(item => item.id === notificationId)
    if (!notification || notification.isRead) return

    try {
      await markNotificationReadApi(notificationId)
      notification.isRead = true
      hasUnreadNotifications.value = notifications.value.some(item => !item.isRead)
      ElMessage.success('标记为已读')
    } catch (error) {
      console.error('标记已读失败:', error)
      ElMessage.error('标记已读失败')
    }
  }

  // 清空通知状态
  const clearNotifications = () => {
    notifications.value = []
    hasUnreadNotifications.value = false
    notificationLoading.value = false
  }

  return {
    token,
    roles,
    username,
    userInfo,
    notifications,
    hasUnreadNotifications,
    notificationLoading,
    roleName,
    setToken,
    getInfo,
    changeRoles,
    logout,
    resetToken,
    fetchNotifications,
    markNotificationAsRead,
    clearNotifications
  }
})

/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useUserStoreOutside() {
  return useUserStore(pinia)
}
