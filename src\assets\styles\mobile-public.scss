.flex_start{
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.flex_start_0{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}
.flex_column{
    flex-direction: column;
}
.flex_wrap{
    flex-wrap: wrap;
}
.flex_between{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.flex_1{
    flex:1
}
p,h3{
    margin: 0;
}
ul{
    list-style: none;
}
.mobile-container{
    max-width:375px;
    width: 100%;
    margin:0 auto;
}
.back{
    background: #F3F4F6;
     border-radius: 4px 4px 4px 4px;
     font-size: 16px;
     color: #09B1EA;
     line-height: 24px;
     border:none;
     width: 90%;
     display: block;
     margin:16px auto 8px auto;
     padding: 12px 0;
  }