import { computed, nextTick, watch } from "vue"
import { tryOnUnmounted, useDebounceFn, useResizeObserver, useTimeoutFn, useWindowSize } from "@vueuse/core"
import echarts from "./echarts"

function useEcharts(chartRef) {
  let chartInstance = null
  let cacheOptions = {}

  const { height, width } = useWindowSize()
  const resizeHandler = useDebounceFn(resize, 200)

  const getOptions = computed(() => {
    return {
      backgroundColor: "transparent",
      ...cacheOptions
    }
  })

  const initCharts = (t) => {
    const el = chartRef?.value?.$el
    if (!el) {
      return
    }
    chartInstance = echarts.init(el, t || null)

    return chartInstance
  }

  const renderEcharts = (options, clear = true) => {
    cacheOptions = options
    return new Promise((resolve) => {
      if (chartRef.value?.offsetHeight === 0) {
        useTimeoutFn(() => {
          renderEcharts(getOptions.value)
          resolve(null)
        }, 30)
        return
      }
      nextTick(() => {
        useTimeoutFn(() => {
          if (!chartInstance) {
            const instance = initCharts()
            if (!instance) return
          }
          clear && chartInstance?.clear()
          chartInstance?.setOption(getOptions.value)
          resolve(null)
        }, 30)
      })
    })
  }

  function resize() {
    chartInstance?.resize({
      animation: {
        duration: 300,
        easing: "quadraticIn"
      }
    })
  }

  watch([width, height], () => {
    resizeHandler?.()
  })

  useResizeObserver(chartRef, resizeHandler)

  // watch(isDark, () => {
  //   if (chartInstance) {
  //     chartInstance.dispose()
  //     initCharts()
  //     renderEcharts(cacheOptions)
  //     resize()
  //   }
  // })

  tryOnUnmounted(() => {
    // 销毁实例，释放资源
    chartInstance?.dispose()
  })
  return {
    renderEcharts,
    resize
  }
}

export { useEcharts }
