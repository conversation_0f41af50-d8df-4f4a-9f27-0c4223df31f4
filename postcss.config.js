export default {
    plugins: {
      'postcss-pxtorem': {
        rootValue:37.5, // 根元素字体大小
        unitPrecision: 5, // 转换后的精度
        propList: ['*'], // 需要转换的属性，*表示全部
        selectorBlackList: [], // 不转换的选择器
        replace: true,
        mediaQuery: false, // 允许在媒体查询中转换px
        minPixelValue: 0, // 最小转换像素值
        // 排除不需要转换的文件夹（关键步骤）
        exclude: (filePath) => {
          // 只转换指定文件夹（如 src/rem-pages）
          return !filePath.includes('/src/views/user/mobile/')
        }
      }
    }
  }