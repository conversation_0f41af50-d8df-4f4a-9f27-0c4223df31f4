<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, Plus } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import {
  getUserList<PERSON>pi,
  createUser<PERSON><PERSON>,
  updateUser<PERSON><PERSON>,
  resetUserPasswordApi,
  getUserDetailApi,
  downloadTemplateApi,
  batchImportApi,
} from "@/apis/user-management";
import { downloadFile } from "@/utils/index";
import { getCityList, getAreaListByCity } from "@/apis/common";

const userStore = useUserStore();

const userInfo = computed(() => {
  return userStore.userInfo
})

// 搜索表单数据
const searchForm = ref({
  userType: "", // 用户类型
  searchKeyword: "", // 手机号/姓名搜索
});

// 根据当前用户角色获取用户类型选项
const getUserTypeOptions = () => {
  const currentRoles = userStore.roles;

  // 省级用户：可以看到地市用户、县区用户
  if (currentRoles.includes("3")) {
    return [
      { label: "全部", value: "" },
      { label: "地市用户", value: "2" },
      { label: "县区用户", value: "1" },
    ];
  }

  // 市级用户：可以看到本市区、县级用户
  if (currentRoles.includes("2")) {
    return [
      { label: "全部", value: "" },
      { label: "本市区", value: "2" },
      { label: "县级用户", value: "1" },
    ];
  }

  // 县区级用户：无用户类型选项
  return [];
};

// 用户类型选项
const userTypeOptions = computed(() => getUserTypeOptions());

// 是否显示用户类型选择
const showUserTypeSelect = computed(() => {
  const currentRoles = userStore.roles;
  return currentRoles.includes("3") || currentRoles.includes("2");
});

// 搜索功能
const handleSearch = async () => {
  try {
    console.log("搜索参数:", searchForm.value);
    // 构建查询参数
    const params = {
      role: searchForm.value.userType,
      nameOrMobile: searchForm.value.searchKeyword,
      cityId: searchForm.value.userType === '1' ? userInfo.value.cityId : "",  // 选县区级用户传地市id
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
    };

    const { data } = await getUserListApi(params);
    tableData.value = data.list || [];
    pagination.value.total = data.total || 0;
  } catch (error) {
    console.error("搜索错误:", error);
  }
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    userType: "",
    searchKeyword: "",
  };
  console.log("表单已重置");
  // 重置后重新搜索
  handleSearch();
};

// 表格数据
const tableData = ref([]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  handleSearch();
};

// 弹窗相关状态
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 重置密码弹窗状态
const resetPasswordDialogVisible = ref(false);
const currentResetUser = ref(null);

// 用户表单数据
const userForm = ref({
  name: "",
  mobile: "",
  role: "",
  cityId: "",
  areaId: "",
});

// 用户表单验证规则 - 动态计算
const userFormRules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    mobile: [
      { required: true, message: "请输入手机号", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    role: [{ required: true, message: "请选择角色", trigger: "change" }],
  };

  // 根据选择的角色动态添加地市和区县的验证规则
  const selectedRole = userForm.value.role;

  // 省级用户（role: "3"）：无需选择地市和区县
  if (selectedRole === "3") {
    // 省级用户不需要地市和区县验证
  }
  // 市级用户（role: "2"）：需要选择地市，无需选择区县
  else if (selectedRole === "2") {
    baseRules.cityId = [{ required: true, message: "请选择所属地市", trigger: "change" }];
  }
  // 县级用户（role: "1"）：需要选择地市和区县
  else if (selectedRole === "1") {
    baseRules.cityId = [{ required: true, message: "请选择所属地市", trigger: "change" }];
    baseRules.areaId = [{ required: true, message: "请选择所属区县", trigger: "change" }];
  }

  return baseRules;
});

// 根据当前用户角色获取可选择的角色选项
const getRoleOptions = () => {
  const currentRoles = userStore.roles;
  const allRoles = [
    { label: "省级用户", value: "3" },
    { label: "地市用户", value: "2" },
    { label: "县区用户", value: "1" },
  ];

  // 省级用户：可以选择省级、市级、县级
  if (currentRoles.includes("3")) {
    return allRoles;
  }
  // 市级用户：可以选择市级、县级
  else if (currentRoles.includes("2")) {
    return allRoles.filter(role => role.value === "2" || role.value === "1");
  }
  // 县级用户：只能选择县级
  else if (currentRoles.includes("1")) {
    return allRoles.filter(role => role.value === "1");
  }

  // 默认返回空数组
  return [];
};

// 角色选项 - 动态计算
const roleOptions = computed(() => getRoleOptions());

// 地市选项
const cityOptions = ref([]);

// 区县选项
const areaOptions = ref([]);

// 加载地市列表
const loadCityList = async () => {
  try {
    const { data } = await getCityList();
    cityOptions.value = data || [];
  } catch (error) {
    console.error("加载地市列表失败:", error);
    ElMessage.error("加载地市列表失败");
  }
};

// 加载区县列表
const loadAreaList = async (cityId) => {
  if (!cityId) {
    areaOptions.value = [];
    return;
  }

  try {
    const { data } = await getAreaListByCity(cityId);
    areaOptions.value = data || [];
  } catch (error) {
    console.error("加载区县列表失败:", error);
    ElMessage.error("加载区县列表失败");
  }
};

// 地市选择变化处理
const handleCityChange = (cityId) => {
  userForm.value.areaId = ""; // 清空区县选择
  loadAreaList(cityId);
};

// 角色选择变化处理
const handleRoleChange = (role) => {
  // 根据角色清空相应的地市和区县选择
  if (role === "3") {
    // 省级用户：清空地市和区县
    userForm.value.cityId = "";
    userForm.value.areaId = "";
    areaOptions.value = [];
  } else if (role === "2") {
    // 市级用户：清空区县
    userForm.value.areaId = "";
    areaOptions.value = [];
  }
  // 县级用户（role === "1"）保持现有选择
};

// 重置密码表单数据
const resetPasswordForm = ref({
  newPassword: "",
  confirmPassword: "",
});

// 密码校验规则函数
const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请输入密码"));
    return;
  }

  // 长度：8-30 位字符
  if (value.length < 8 || value.length > 30) {
    callback(new Error("密码长度必须为8-30位字符"));
    return;
  }

  // 至少 1 个大写字母（A-Z）
  if (!/[A-Z]/.test(value)) {
    callback(new Error("密码必须包含至少1个大写字母"));
    return;
  }

  // 至少 1 个小写字母（a-z）
  if (!/[a-z]/.test(value)) {
    callback(new Error("密码必须包含至少1个小写字母"));
    return;
  }

  // 至少 1 个数字（0-9）
  if (!/[0-9]/.test(value)) {
    callback(new Error("密码必须包含至少1个数字"));
    return;
  }

  // 至少 1 个特殊字符
  if (!/[!@#$%^&*,.?":{}|<>]/.test(value)) {
    callback(new Error("密码必须包含至少1个特殊字符(!@#$%^&*,.等)"));
    return;
  }

  callback();
};

// 重置密码表单验证规则
const resetPasswordRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { validator: validatePassword, trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== resetPasswordForm.value.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 表单引用
const userFormRef = ref();
const resetPasswordFormRef = ref();

// 新增用户
const handleAddUser = () => {
  dialogTitle.value = "新增用户";
  isEdit.value = false;
  userForm.value = {
    name: "",
    mobile: "",
    role: "",
    cityId: "",
    areaId: "",
  };
  areaOptions.value = []; // 清空区县选项
  dialogVisible.value = true;
};

// 批量导入相关状态
const batchImportVisible = ref(false);
const uploadedFile = ref(null);
const uploadRef = ref();

// 批量导入
const handleBatchImport = () => {
  batchImportVisible.value = true;
};

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const response = await downloadTemplateApi();
    downloadFile(
      response,
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "用户导入模板.xlsx"
    );
    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  }
};

// 文件上传前的检查
const beforeUpload = (file) => {
  const isExcel =
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.type === "application/vnd.ms-excel";
  if (!isExcel) {
    ElMessage.error("只能上传 Excel 文件!");
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error("上传文件大小不能超过 10MB!");
    return false;
  }
  return true;
};

// 文件上传成功
const handleUploadSuccess = (file) => {
  uploadedFile.value = file.raw;
  ElMessage.success("文件上传成功");
};

// 删除上传的文件
const handleRemoveFile = () => {
  uploadedFile.value = null;
  uploadRef.value?.clearFiles();
};

// 确认导入
const handleConfirmImport = async () => {
  if (!uploadedFile.value) {
    ElMessage.error("请先上传文件");
    return;
  }

  try {
    const formData = new FormData();
    formData.append("file", uploadedFile.value);

    await batchImportApi(formData);
    ElMessage.success("批量导入成功");
    batchImportVisible.value = false;
    uploadedFile.value = null;
    uploadRef.value?.clearFiles();
    // 刷新列表
    handleSearch();
  } catch (error) {
    console.error("批量导入失败:", error);
  }
};

// 关闭批量导入弹窗
const handleCloseBatchImport = () => {
  batchImportVisible.value = false;
  uploadedFile.value = null;
  uploadRef.value?.clearFiles();
};

// 检查是否有权限编辑指定角色的用户
const canEditUserRole = (targetRole) => {
  const currentRoles = userStore.roles;
  const currentRoleLevel = currentRoles.includes("3") ? 3 : currentRoles.includes("2") ? 2 : 1;
  const targetRoleLevel = parseInt(targetRole);

  // 只能编辑同级或下级用户
  return currentRoleLevel >= targetRoleLevel;
};

// 编辑用户
const handleEditUser = async (row) => {
  try {
    // 检查权限
    if (!canEditUserRole(row.role?.toString())) {
      ElMessage.error("您没有权限编辑该级别的用户");
      return;
    }

    dialogTitle.value = "编辑用户";
    isEdit.value = true;

    // 先请求用户详情
    const { data } = await getUserDetailApi(row.id);

    userForm.value = {
      id: data.id,
      name: data.title || data.name,
      mobile: data.mobile || data.mobile,
      role: data.role?.toString() || "",
      cityId: data.cityId?.toString() || "",
      areaId: data.areaId?.toString() || "",
    };

    // 如果有cityId，加载对应的区县列表
    if (data.cityId) {
      await loadAreaList(data.cityId);
    }

    dialogVisible.value = true;
  } catch (error) {
    console.error("获取用户详情失败:", error);
    ElMessage.error("获取用户详情失败");
  }
};

// 提交用户表单
const handleSubmitUser = async () => {
  try {
    await userFormRef.value.validate();

    const params = {
      title: userForm.value.name,
      mobile: userForm.value.mobile,
      role: userForm.value.role,
    };

    // 根据角色添加相应的地市和区县信息
    const selectedRole = userForm.value.role;

    if (selectedRole === "2") {
      // 市级用户：需要地市信息
      params.cityId = userForm.value.cityId;
    } else if (selectedRole === "1") {
      // 县级用户：需要地市和区县信息
      params.cityId = userForm.value.cityId;
      params.areaId = userForm.value.areaId;
    }
    // 省级用户（role === "3"）不需要地市和区县信息

    if (isEdit.value) {
      // 编辑用户
      await updateUserApi(userForm.value.id, params);
      ElMessage.success("用户信息更新成功");
    } else {
      // 新增用户
      await createUserApi(params);
      ElMessage.success("用户创建成功");
    }

    handleCloseDialog();
    handleSearch(); // 刷新列表
  } catch (error) {
    console.error("提交用户表单失败:", error);
    ElMessage.error(isEdit.value ? "更新用户失败" : "创建用户失败");
  }
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  userFormRef.value?.resetFields();
};

// 重置密码
const handleResetPassword = (row) => {
  currentResetUser.value = row;
  resetPasswordForm.value = {
    newPassword: "",
    confirmPassword: "",
  };
  resetPasswordDialogVisible.value = true;
};

// 关闭重置密码弹窗
const handleCloseResetPasswordDialog = () => {
  resetPasswordDialogVisible.value = false;
  resetPasswordFormRef.value?.resetFields();
  currentResetUser.value = null;
};

// 提交重置密码
const handleSubmitResetPassword = async () => {
  try {
    await resetPasswordFormRef.value.validate();

    const params = {
      password: resetPasswordForm.value.newPassword,
    };

    await resetUserPasswordApi(currentResetUser.value.id, params);
    ElMessage.success(
      `用户 ${getRoleLabel(currentResetUser.value.role)} 的密码重置成功`
    );
    handleCloseResetPasswordDialog();
  } catch (error) {
    console.error("重置密码失败:", error);
    ElMessage.error("重置密码失败");
  }
};

// 操作处理
const handleOperation = async (row, action) => {
  if (action === "edit") {
    handleEditUser(row);
  } else if (action === "reset-password") {
    handleResetPassword(row);
  }
};

// 获取角色CSS类名
const getRoleClass = (role) => {
  const classMap = {
    2: "shi-role",
    1: "qu-role",
    3: "sheng-role",
  };
  return classMap[role] || "role-tag";
};

// 获取角色显示名称
const getRoleLabel = (role) => {
  const labelMap = {
    2: "地市用户",
    1: "县区用户",
    3: "省级用户",
  };
  return labelMap[role] || role;
};

// 页面挂载时初始化
onMounted(() => {
  loadCityList();
  handleSearch();
});
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="用户管理"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-3" label-position="top">
        <!-- 左侧表单项 -->
        <div class="form-left">
          <!-- 用户类型选择 -->
          <el-form-item v-if="showUserTypeSelect" label="用户类型">
            <el-select v-model="searchForm.userType" placeholder="全部">
              <el-option
                v-for="item in userTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <!-- 手机号/姓名搜索 -->
          <el-form-item label="手机号/姓名搜索">
            <el-input
              v-model="searchForm.searchKeyword"
              placeholder="请输入手机号或姓名"
              style="width: 240px"
            />
          </el-form-item>
        </div>

        <!-- 右侧按钮组 -->
        <div class="form-right">
          <el-form-item class="hidden-label" label="按钮">
            <el-button class="common-button-3" @click="handleSearch">
              <template #icon>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
              查询
            </el-button>
            <el-button class="reset-button" @click="handleReset">
              <template #icon>
                <el-icon><RefreshRight /></el-icon>
              </template>
              重置
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 操作按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4" @click="handleRefresh">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <div class="flex gap-[16px]">
          <el-button class="common-button-3" @click="handleAddUser">
            <template #icon>
              <el-icon><Plus /></el-icon>
            </template>
            新增用户
          </el-button>
          <el-button class="common-button-3" @click="handleBatchImport">
            <template #icon>
              <img
                src="@/assets/images/common/export.png"
                alt=""
                width="16"
                height="16"
              />
            </template>
            批量导入
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 序号 -->
        <el-table-column prop="id" label="序号" width="80" align="center" />

        <!-- 姓名 -->
        <el-table-column
          prop="title"
          label="姓名"
          min-width="120"
          align="center"
        >
          <template #default="{ row }">
            {{ row.title || "-" }}
          </template>
        </el-table-column>

        <!-- 手机号 -->
        <el-table-column
          prop="mobile"
          label="手机号"
          min-width="140"
          align="center"
        >
          <template #default="{ row }">
            {{ row.mobile || "-" }}
          </template>
        </el-table-column>

        <!-- 角色 -->
        <el-table-column label="角色" min-width="120" align="center">
          <template #default="{ row }">
            <div :class="['role-tag', getRoleClass(row.role)]">
              {{ getRoleLabel(row.role) }}
            </div>
          </template>
        </el-table-column>

        <!-- 地市 -->
        <el-table-column
          prop="cityName"
          label="地市"
          min-width="120"
          align="center"
        >
          <template #default="{ row }">
            {{ row.cityName || "-" }}
          </template>
        </el-table-column>

        <!-- 区县 -->
        <el-table-column
          prop="areaName"
          label="区县"
          min-width="120"
          align="center"
        >
          <template #default="{ row }">
            {{ row.areaName || "-" }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="flex-center-center gap-[16px]">
              <!-- 只有有权限的用户才能看到编辑按钮 -->
              <div
                v-if="canEditUserRole(row.role?.toString())"
                class="pointer blue2"
                @click="handleOperation(row, 'edit')"
              >
                编辑
              </div>
              <!-- 只有有权限的用户才能看到重置密码按钮 -->
              <div
                v-if="canEditUserRole(row.role?.toString())"
                class="pointer red1"
                @click="handleOperation(row, 'reset-password')"
              >
                重置密码
              </div>
              <!-- 无权限时显示提示 -->
              <div v-if="!canEditUserRole(row.role?.toString())" class="text-gray-400">
                无权限操作
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 用户弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-position="top"
        class="user-form"
      >
        <el-form-item label="姓名" prop="name" required>
          <el-input
            v-model="userForm.name"
            placeholder="请输入姓名"
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="mobile" required>
          <el-input
            v-model="userForm.mobile"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="角色" prop="role" required>
          <el-select
            v-model="userForm.role"
            placeholder="请选择角色"
            style="width: 100%"
            @change="handleRoleChange"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 地市选择 - 市级和县级用户需要 -->
        <el-form-item
          v-if="userForm.role === '2' || userForm.role === '1'"
          label="所属地市"
          prop="cityId"
          required
        >
          <el-select
            v-model="userForm.cityId"
            placeholder="请选择所属地市"
            style="width: 100%"
            @change="handleCityChange"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 区县选择 - 只有县级用户需要 -->
        <el-form-item
          v-if="userForm.role === '1'"
          label="所属区县"
          prop="areaId"
          required
        >
          <el-select
            v-model="userForm.areaId"
            placeholder="请选择所属区县"
            style="width: 100%"
            :disabled="!userForm.cityId"
          >
            <el-option
              v-for="item in areaOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCloseDialog"
            >取消</el-button
          >
          <el-button class="confirm-btn" @click="handleSubmitUser"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码弹窗 -->
    <el-dialog
      v-model="resetPasswordDialogVisible"
      title="重置密码"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseResetPasswordDialog"
    >
      <el-form
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        label-position="top"
        class="reset-password-form"
      >
        <el-form-item label="新密码" prop="newPassword" required>
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword" required>
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            maxlength="20"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCloseResetPasswordDialog"
            >取消</el-button
          >
          <el-button class="confirm-btn" @click="handleSubmitResetPassword"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入弹窗 -->
    <el-dialog
      v-model="batchImportVisible"
      class="custom-dialog"
      title="批量导入"
      width="480px"
      :close-on-click-modal="false"
      @close="handleCloseBatchImport"
    >
      <div class="batch-import-content">
        <div class="import-header">
          <span class="required-mark">*</span>
          <span class="text-14-20-600 grey1">批量导入</span>
          <el-upload
            ref="uploadRef"
            :before-upload="beforeUpload"
            :on-change="handleUploadSuccess"
            :auto-upload="false"
            :show-file-list="false"
            accept=".xlsx,.xls"
            style="display: inline-block; margin-left: 20px"
          >
            <el-button class="upload-btn">
              <template #icon>
                <img
                  src="@/assets/images/common/upload.png"
                  alt=""
                  width="16"
                  height="16"
                />
              </template>
              上传
            </el-button>
          </el-upload>
          <div
            @click="handleDownloadTemplate"
            class="pointer"
            style="color: #239dde; margin-left: 10px"
          >
            模版下载
          </div>
        </div>

        <!-- 已上传文件显示 -->
        <div v-if="uploadedFile" class="uploaded-file">
          <div class="file-info">
            <span class="file-name">{{ uploadedFile.name }}</span>
            <div
              @click="handleRemoveFile"
              style="color: #f56c6c; cursor: pointer;"
            >
              删除
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCloseBatchImport"
            >取消</el-button
          >
          <el-button class="confirm-btn" @click="handleConfirmImport"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 角色标签样式
.role-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.role-2 {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }

  &.role-1 {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.role-3 {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }

  &.role-default {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
  }
}

// 用户表单样式
.user-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }

    .el-select {
      .el-select__wrapper {
        height: 48px;
        font-size: 16px;
      }
    }
  }
}

// 重置密码表单样式
.reset-password-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }
  }
}

// 批量导入弹窗样式
:deep(.batch-import-content) {
  .import-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .required-mark {
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .upload-btn {
    background: #239dde;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #239dde;
    color: #fff;
    font-size: 14px;
  }

  .uploaded-file {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    background-color: #f5f7fa;
    margin-top: 20px;

    .file-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .file-name {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}
</style>
