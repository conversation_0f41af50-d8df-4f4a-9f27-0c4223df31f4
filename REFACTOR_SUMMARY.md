# 按钮重构总结

## 重构目标
将页面中重复的保存和提交按钮代码统一，避免在模板中使用v-if重复书写，改为在函数中根据type和userStore.userInfo.role来区分使用哪个接口。

## 重构内容

### 1. 新增统一函数

#### handleSave() - 统一保存函数
- 根据 `type` 参数决定保存哪种类型的草稿
- 支持的类型：`distribution`、`review`、`reply`、`add-supervise`
- 统一显示保存成功消息

#### handleSubmit() - 统一提交函数
- 根据 `type` 和 `userStore.userInfo.role` 决定调用哪个接口
- 统一权限检查（role === 2）
- 统一错误处理
- 支持的类型：
  - `distribution` → `submitAssignment()`
  - `review` → `submitReview()`
  - `reply` → `submitReply()`
  - `add-supervise` → `submitSupervise()`

### 2. 简化原有函数
移除了原有提交函数中的权限检查逻辑，因为权限检查已经在统一函数中处理：
- `submitAssignment()` - 分办提交
- `submitReview()` - 审核提交
- `submitReply()` - 回复提交
- `submitSupervise()` - 督办意见提交（新增）

### 3. 模板简化
将原来的三套重复按钮代码：
```vue
<!-- 分办页面按钮 -->
<template v-if="type === 'distribution'">
  <el-button @click="saveDraft">保存</el-button>
  <el-button @click="submitAssignment">提交</el-button>
</template>

<!-- 审核页面按钮 -->
<template v-if="type === 'review'">
  <el-button @click="saveDraft">保存</el-button>
  <el-button @click="submitReview">提交</el-button>
</template>

<!-- 回复页面按钮 -->
<template v-if="type === 'reply'">
  <el-button @click="saveDraft">保存</el-button>
  <el-button @click="submitReply">提交</el-button>
</template>
```

简化为一套统一按钮：
```vue
<!-- 统一的保存和提交按钮 -->
<template v-if="['distribution', 'review', 'reply', 'add-supervise'].includes(type)">
  <el-button @click="handleSave">保存</el-button>
  <el-button @click="handleSubmit">提交</el-button>
</template>
```

## 优势
1. **代码复用**：消除了重复的按钮代码
2. **统一管理**：权限检查和错误处理集中管理
3. **易于维护**：新增类型只需在统一函数中添加case
4. **逻辑清晰**：业务逻辑与UI展示分离
5. **扩展性好**：支持新的操作类型（如add-supervise）

## 测试建议
建议测试以下场景：
1. 不同type值下的保存和提交功能
2. 不同用户角色的权限控制
3. 各种错误情况的处理
4. 文件上传相关功能
