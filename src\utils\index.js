import dayjs from "dayjs"
import { removeLayoutsConfig } from "./cache/local-storage"

/** 格式化时间 */
export function formatDateTime(time) {
  return time ? dayjs(new Date(time)).format("YYYY-MM-DD HH:mm:ss") : "N/A"
}

/** 格式化日期 */
export function formatDate(time) {
  return time ? dayjs(new Date(time)).format("YYYY-MM-DD") : "N/A"
}

/** 用 JS 获取全局 css 变量 */
export function getCssVariableValue(cssVariableName) {
  let cssVariableValue = ""
  try {
    // 没有拿到值时，会返回空串
    cssVariableValue = getComputedStyle(document.documentElement).getPropertyValue(cssVariableName)
  } catch (error) {
    console.error(error)
  }
  return cssVariableValue
}

/** 用 JS 设置全局 CSS 变量 */
export function setCssVariableValue(cssVariableName, cssVariableValue) {
  try {
    document.documentElement.style.setProperty(cssVariableName, cssVariableValue)
  } catch (error) {
    console.error(error)
  }
}

/** 重置项目配置 */
export function resetConfigLayout() {
  removeLayoutsConfig()
  location.reload()
}

// 获取url参数
export function getHashSearchParam(key, url = window.location.href) {
  // const url = window.location.href
  // 获取 hash 值，不包含 '#' 号
  const hash = url.substring(url.indexOf("#") + 1)
  // 查找 '?' 号所在的索引
  const searchIndex = hash.indexOf("?")
  // '?' 号后面接的是索引参数，如果找到则+1，去除'?' 号
  const search = searchIndex !== -1 ? hash.substring(searchIndex + 1) : ""
  // 把搜索参数字符串提过URLSearchParams转成对象形式
  const usp = new URLSearchParams(search)
  // 通过URLSearchParams自带的get方法，查询键所对应的值
  return usp.get(key)
}

// 导出 excel 文件
export function downloadExcelFile(binaryData, filename) {
  // 创建一个Blob对象，这里假设binaryData是一个Uint8Array或ArrayBuffer
  const blob = new Blob([binaryData], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" })

  // 创建一个指向Blob的URL
  const url = window.URL.createObjectURL(blob)

  // 创建一个a元素用于下载
  const a = document.createElement("a")
  a.href = url
  a.download = filename || "download.xlsx"

  // 将a元素添加到文档中，并触发点击事件
  document.body.appendChild(a)
  a.click()

  // 释放URL对象
  window.URL.revokeObjectURL(url)

  // 移除a元素
  document.body.removeChild(a)
}

export function getdate(type = "") {
  const myDate = new Date()
  const year = myDate.getFullYear() // 获取当前年
  const mon = myDate.getMonth() + 1 // 获取当前月
  const date = myDate.getDate() // 获取当前日
  const sp = "-"
  if (type === "month") {
    return year + sp + mon
  } else {
    return year + sp + mon + sp + date
  }
}

export function randomString(len, charSet) {
  charSet = charSet || "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let randomString = ""
  for (let i = 0; i < len; i++) {
    const randomPoz = Math.floor(Math.random() * charSet.length)
    randomString += charSet.substring(randomPoz, randomPoz + 1)
  }
  return randomString
}

export function downloadFile(res, type, fileName) {
  const blob = new Blob([res], {
    type
  })
  const a = document.createElement("a")
  const URL = window.URL || window.webkitURL
  const href = URL.createObjectURL(blob)
  a.href = href
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  window.URL.revokeObjectURL(href)
}

/**
 * 比较两个对象中共有属性是否完全相同
 * @param obj1 第一个对象
 * @param obj2 第二个对象
 * @returns boolean 如果共有属性都相同返回true，否则返回false
 */
export function compareCommonProperties(obj1, obj2){
  // 获取两个对象的所有属性键
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  // 找出共有的属性键
  const commonKeys = keys1.filter(key => keys2.includes(key))

  // 比较每个共有属性的值
  return commonKeys.every(key => obj1[key] === obj2[key])
}
