// Mixins
@use "./mixins.scss" as *;
// 全局 CSS 变量
@use "./variables.css" as *;
// Transition
@use "./transition.scss" as *;
// Element Plus
@use "./element-plus.css" as *;
@use "./element-plus.scss" as *;
// Vxe Table
@use "./vxe-table.css" as *;
@use "./vxe-table.scss" as *;
// 注册多主题
@use "./theme/register.scss" as *;
// View Transition
@use "./view-transition.scss" as *;
// 自定义全局样式
@use "./custom-global-style.scss" as *;

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 18px 16px;
}

html {
  height: 100%;
  // 灰色模式
  &.grey-mode {
    filter: grayscale(1);
  }
  // 色弱模式
  &.color-weakness {
    filter: invert(0.8);
  }
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family:
    Inter, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}
