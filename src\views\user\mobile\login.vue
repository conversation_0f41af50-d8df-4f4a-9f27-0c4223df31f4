<template>
    <div class="mobile-container flex_start flex_column">
          <img src="/assets/imgs/logo.png" alt="" class="logo" />
          <img src="/assets/imgs/middle.png" alt="" class="middle" />
          <div class="box flex_center flex_column">
               <p class="line1">投诉查询</p>
               <p class="line2">查询办理情况</p>
              <div class="form">
                  <div class="form-item">
                      <p class="label">手机号</p>
                      <div class="flex_start ipt">
                           <img src="/assets/imgs/person.png" alt="" />
                           <input type="text" placeholder="请输入手机号" />
                      </div>
                  </div>
                  <div class="form-item">
                      <p class="label">验证码</p>
                      <div class="flex_start ipt ">
                            <img src="/assets/imgs/lock.png" alt="" />
                            <input type="text" placeholder="请输入验证码" class="code-ipt" />
                            <p class="code">获取手机验证码</p>
                      </div>
                  </div>
              </div>
              <button class="search">查询</button>
          </div>
          <div class="footer flex_start flex_column">
             <p>中共江苏省委教育工委 江苏省教育厅 </p>
             <p>江苏省纪委监委驻省教育厅纪检监察组</p>
             <p>主办</p>
          </div>
    </div>
</template>
<script setup>
import {ref,reactive} from 'vue'
let form=reactive({
    name:''
})
</script>
<style scoped lang='scss'>
@use '@/assets/styles/mobile-public.scss';
.mobile-container{
  
    font-size: 12px;
    background: url(/assets/imgs/bg.png) no-repeat;
    background-size: 100% 100%;
    min-height:100vh;
    .logo{
        width: 180px;
        height: auto;
        margin-top: 36px;
    }
    .middle{
        width: 289px;
        height: auto;
        margin-top: 26px;
    }
    .box{
          padding: 24px;
          background: #fff;
          border-radius: 12px;
          width:95%;
          max-width: 343px;
          margin-top: 37px;
          .line1{
            font-size: 24px;
            color: #05AFE8;
            line-height: 35px;
            text-align: center;
          }
          .line2{
            font-size: 14px;
            color: #2B2C33;
            text-align: center;
            margin-top: 12px;
            line-height: 21px;
          }
          .form{
            .form-item{
                margin-top: 16px;
                .label{
                    font-size: 12px;
                    color: #6C7278;
                    line-height: 19px;
                    margin-bottom: 2px;
                }
               .ipt{
                    background: #FFFFFF;
                    box-shadow: 0px 1px 2px 0px rgba(228,229,231,0.24);
                    border-radius: 4px 4px 4px 4px;
                    border: 1px solid #E2E3E6;
                    padding:2px 14px;
                     height: 42px;
                    input{
                        height: 100%;
                        border: none;
                        outline: none;
                        flex:1;
                        font-size: 14px;
                    }
                   img{
                        width: 16px;
                        height: auto;
                        margin-right: 12px;
                   }
                   .code-ipt{
                    width: 100px;
                   }
                   .code{
                    color: #02AFE7;
                    font-size: 14px;white-space: nowrap;
                    cursor: pointer;
                   }
               }
            }
          }
          .search{
            font-size: 14px;
            color: #FFFFFF;
            padding: 14px 0;
            width: 100%;
            background: linear-gradient( 270deg, #36B8FC 0%, #02AFE7 100%);
            border-radius: 4px 4px 4px 4px;
            border:none;
            margin-top: 24px;
          }
    }
    .footer{
        font-weight: bold;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0px 2px 4px rgba(0,0,0,0.7203);
        text-align: center;
        position: absolute;
        bottom: 8px;
    }
}
 @media screen and (min-width:750px){
   .mobile-container{
    .footer{
      position:static;
      margin-top: 70px;
      margin-bottom: 10px;
   }
   }
   
 }
</style>