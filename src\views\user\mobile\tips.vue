<template>
    <div class="mobile-container">
        <p class="title">投诉列表</p>
        <div class="list">
             <div class="item flex_between" v-for="item in 2" :key="item">
                 <div class="flex_start left">
                     <img src="@/assets/images/user/schooldress.png" alt="">
                     <div>
                        <div class="desc">
                             <div class="flex_start">您举报<div class="type1">南京市 建邺区 光明小学</div></div>
                             <div class="flex_start">在 <div class="type1">校服定制采购</div> 存在问题</div>
                        </div>
                        <p class="date">2025-09-09</p>
                     </div>
                 </div>
                 <div class="flex_start right">
                     <img src="@/assets/images/user/handleing.png" alt="">
                     <p>已处理</p>
                 </div>
            </div>
        </div>
         <button class="back">返回</button>
    </div>
</template>
<style scoped lang='scss'>
@use '@/assets/styles/mobile-public.scss';
.mobile-container{
    background:#f9fafb url(@/assets/images/user/tips-bg.png) no-repeat;
    background-size: 100% 40vh;
    min-height: 100vh;
    overflow: auto;
    .title{
        font-size: 20px;
        color: #FFFFFF;
        line-height: 29px;
        text-align: center;
        padding-top: 12px;
    }
    .list{
        width: 100%;height: calc(100vh - 112px);overflow: auto;
    }
    .item{
        cursor: pointer;
        padding: 12px;
        border-radius: 12px;
      background: rgba(255,255,255,0.999);
         box-shadow: 0px 1px 0px 0px #E6E6E6;
        width: 90%;
        max-width: 350px;
        margin: 12px auto 0 auto;
       
        .left{
            img{
                width: 38px;
                height: auto;
                margin-right: 8px;
            }
            .desc{
                font-size: 14px;
                color: #2B2C33;
                line-height: 20px;
                .type1{
                    color: #239DDE;
                    
                }
                .type2{
                    color: #FF8C16;
                }
                .type3{
                    color: #EC70BE;
                }
            
            }
            .date{
                font-size: 14px;
                color: #B4B6BE;
                line-height: 20px;
                margin-top: 4px;
            }
        }
        .right{
            img{
                width: 16px;
                height: auto;
                margin-right: 4px;
            }
            p{
                font-size: 13px;
                color: #28B28B;
                line-height: 16px;
            }
            .nohandle{
                color: #94959C
            }
            .handleing{
                color: #508CFF;
            }
        }
    }
   
}
</style>